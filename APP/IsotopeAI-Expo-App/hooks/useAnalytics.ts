import { useState, useEffect, useCallback, useRef } from 'react';
import {
  Analytics,
  DailyStat,
  WeeklyStat,
  MonthlyStat,
  SubjectStat,
  TaskTypeStat,
  StreakInfo,
  DateRange,
  ChartDataPoint,
  TimeSeriesDataPoint,
  AnalyticsPeriod
} from '@/types/app';
import { analyticsService } from '@/services/analyticsService';
import { enhancedSupabaseService } from '@/services/enhancedSupabaseService';
import { useAuth } from '@/contexts/AuthContext';

interface AnalyticsState {
  analytics: Analytics | null;
  quickStats: {
    todayTime: number;
    weekTime: number;
    monthTime: number;
    currentStreak: number;
    totalSessions: number;
  } | null;
  timeSeriesData: TimeSeriesDataPoint[];
  subjectChartData: ChartDataPoint[];
  taskTypeChartData: ChartDataPoint[];
  performanceMetrics: any | null;
  loading: boolean;
  error: string | null;
}

export function useAnalytics() {
  const { user } = useAuth();
  const [state, setState] = useState<AnalyticsState>({
    analytics: null,
    quickStats: null,
    timeSeriesData: [],
    subjectChartData: [],
    taskTypeChartData: [],
    performanceMetrics: null,
    loading: false,
    error: null
  });

  const [selectedPeriod, setSelectedPeriod] = useState<AnalyticsPeriod>('week');
  const [dateRange, setDateRange] = useState<DateRange>(() => {
    const end = new Date();
    const start = new Date();
    start.setDate(start.getDate() - 7); // Default to last 7 days
    return { start, end };
  });

  // Real-time subscription management
  const subscriptionRef = useRef<string | null>(null);
  const isSubscribedRef = useRef(false);

  // Update date range when period changes
  useEffect(() => {
    const end = new Date();
    let start: Date;

    switch (selectedPeriod) {
      case 'week':
        start = new Date();
        start.setDate(start.getDate() - 7);
        break;
      case 'month':
        start = new Date();
        start.setMonth(start.getMonth() - 1);
        break;
      case 'year':
        start = new Date();
        start.setFullYear(start.getFullYear() - 1);
        break;
      case 'all':
        start = new Date(2020, 0, 1); // Far back date
        break;
      default:
        start = new Date();
        start.setDate(start.getDate() - 7);
    }

    setDateRange({ start, end });
  }, [selectedPeriod]);

  const loadQuickStats = useCallback(async () => {
    if (!user) return;

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      const response = await analyticsService.getQuickStats(user.id);
      
      if (response.success && response.data) {
        setState(prev => ({ 
          ...prev, 
          quickStats: response.data!,
          loading: false 
        }));
      } else {
        setState(prev => ({ 
          ...prev, 
          error: response.error?.message || 'Failed to load quick stats',
          loading: false 
        }));
      }
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: 'Failed to load quick stats',
        loading: false 
      }));
    }
  }, [user]);

  const loadCompleteAnalytics = useCallback(async () => {
    if (!user) return;

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      const response = await analyticsService.calculateCompleteAnalytics(user.id, dateRange);
      
      if (response.success && response.data) {
        setState(prev => ({ 
          ...prev, 
          analytics: response.data!,
          loading: false 
        }));
      } else {
        setState(prev => ({ 
          ...prev, 
          error: response.error?.message || 'Failed to load analytics',
          loading: false 
        }));
      }
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: 'Failed to load analytics',
        loading: false 
      }));
    }
  }, [user, dateRange]);

  const loadChartData = useCallback(async () => {
    if (!user) return;

    try {
      const [timeSeriesResponse, subjectResponse, taskTypeResponse] = await Promise.all([
        analyticsService.generateTimeSeriesData(user.id, dateRange, 'daily'),
        analyticsService.generateSubjectChartData(user.id, dateRange),
        analyticsService.generateTaskTypeChartData(user.id, dateRange)
      ]);

      setState(prev => ({
        ...prev,
        timeSeriesData: timeSeriesResponse.success ? timeSeriesResponse.data! : [],
        subjectChartData: subjectResponse.success ? subjectResponse.data! : [],
        taskTypeChartData: taskTypeResponse.success ? taskTypeResponse.data! : []
      }));
    } catch (error) {
      console.error('Error loading chart data:', error);
    }
  }, [user, dateRange]);

  const loadPerformanceMetrics = useCallback(async () => {
    if (!user) return;

    try {
      const response = await analyticsService.calculatePerformanceMetrics(user.id, dateRange);
      
      if (response.success && response.data) {
        setState(prev => ({ 
          ...prev, 
          performanceMetrics: response.data!
        }));
      }
    } catch (error) {
      console.error('Error loading performance metrics:', error);
    }
  }, [user, dateRange]);

  // Load data when user or date range changes
  useEffect(() => {
    if (user) {
      loadQuickStats();
      loadCompleteAnalytics();
      loadChartData();
      loadPerformanceMetrics();
    }
  }, [user, loadQuickStats, loadCompleteAnalytics, loadChartData, loadPerformanceMetrics]);

  // Setup real-time subscription when user is available
  useEffect(() => {
    if (user) {
      setupRealtimeSubscription();
    }

    // Cleanup on unmount or user change
    return () => {
      cleanupRealtimeSubscription();
    };
  }, [user, setupRealtimeSubscription, cleanupRealtimeSubscription]);

  const refreshAnalytics = useCallback(async () => {
    if (user) {
      await Promise.all([
        loadQuickStats(),
        loadCompleteAnalytics(),
        loadChartData(),
        loadPerformanceMetrics()
      ]);
    }
  }, [user, loadQuickStats, loadCompleteAnalytics, loadChartData, loadPerformanceMetrics]);

  // Real-time subscription setup
  const setupRealtimeSubscription = useCallback(async () => {
    if (!user || isSubscribedRef.current) return;

    try {
      console.log('Setting up real-time analytics subscription for user:', user.id);

      const channelName = await enhancedSupabaseService.subscribeToStudySessions(
        user.id,
        (sessions) => {
          console.log('Real-time analytics update received:', sessions.length, 'sessions');
          // Refresh all analytics data when sessions change
          refreshAnalytics();
        }
      );

      subscriptionRef.current = channelName;
      isSubscribedRef.current = true;
      console.log('Analytics real-time subscription established:', channelName);
    } catch (error) {
      console.error('Error setting up analytics real-time subscription:', error);
    }
  }, [user, refreshAnalytics]);

  // Cleanup real-time subscription
  const cleanupRealtimeSubscription = useCallback(() => {
    if (subscriptionRef.current) {
      console.log('Cleaning up analytics real-time subscription:', subscriptionRef.current);
      enhancedSupabaseService.unsubscribe(subscriptionRef.current);
      subscriptionRef.current = null;
      isSubscribedRef.current = false;
    }
  }, []);

  const changePeriod = useCallback((period: AnalyticsPeriod) => {
    setSelectedPeriod(period);
  }, []);

  const changeCustomDateRange = useCallback((newDateRange: DateRange) => {
    setDateRange(newDateRange);
    setSelectedPeriod('custom');
  }, []);

  // Computed values
  const todayProgress = state.quickStats ? {
    current: Math.round(state.quickStats.todayTime / 60), // Convert to minutes
    target: 60, // Default target, should come from user profile
    percentage: Math.min((state.quickStats.todayTime / (60 * 60)) * 100, 100) // Percentage of 1 hour target
  } : null;

  const weekProgress = state.quickStats ? {
    current: Math.round(state.quickStats.weekTime / 60),
    target: 420, // 7 hours per week default
    percentage: Math.min((state.quickStats.weekTime / (420 * 60)) * 100, 100)
  } : null;

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const formatTimeDetailed = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    }
    return `${secs}s`;
  };

  const getStreakMessage = (): string => {
    if (!state.analytics?.streakInfo) return '';
    
    const { currentStreak, isActiveToday } = state.analytics.streakInfo;
    
    if (currentStreak === 0) {
      return 'Start your study streak today!';
    } else if (currentStreak === 1) {
      return isActiveToday ? 'Great start! Keep it going tomorrow.' : 'You have a 1-day streak. Study today to continue!';
    } else {
      return isActiveToday 
        ? `Amazing! You're on a ${currentStreak}-day streak!` 
        : `You have a ${currentStreak}-day streak. Study today to continue!`;
    }
  };

  const getMostProductiveTimeOfDay = (): string => {
    if (!state.analytics?.dailyStats) return '';
    
    // This would require more detailed session data with timestamps
    // For now, return a placeholder
    return 'Morning (9-11 AM)';
  };

  return {
    // State
    ...state,
    selectedPeriod,
    dateRange,
    
    // Actions
    refreshAnalytics,
    changePeriod,
    changeCustomDateRange,
    
    // Computed values
    todayProgress,
    weekProgress,
    
    // Utilities
    formatTime,
    formatTimeDetailed,
    getStreakMessage,
    getMostProductiveTimeOfDay,
    
    // Quick access to specific data
    dailyStats: state.analytics?.dailyStats || [],
    weeklyStats: state.analytics?.weeklyStats || [],
    monthlyStats: state.analytics?.monthlyStats || [],
    subjectStats: state.analytics?.subjectStats || [],
    taskTypeStats: state.analytics?.taskTypeStats || [],
    streakInfo: state.analytics?.streakInfo || null,
  };
}
