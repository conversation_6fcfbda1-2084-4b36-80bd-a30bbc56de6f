import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { TimerSession, SessionSummary, ApiResponse } from '@/types/app';
import { enhancedSupabaseService } from '@/services/enhancedSupabaseService';

interface SessionState {
  // Session data
  sessions: TimerSession[];
  currentSession: TimerSession | null;
  isLoading: boolean;
  error: string | null;
  lastFetch: number | null;
  
  // Session management
  fetchSessions: (userId: string, forceRefresh?: boolean) => Promise<void>;
  createSession: (userId: string, sessionData: Omit<TimerSession, 'id'>) => Promise<TimerSession>;
  updateSession: (sessionId: string, updates: Partial<TimerSession>) => Promise<TimerSession>;
  completeSession: (sessionId: string, summary: SessionSummary) => Promise<TimerSession>;
  
  // Current session management
  startSession: (userId: string, sessionData: Partial<TimerSession>) => Promise<TimerSession>;
  pauseSession: (sessionId: string) => Promise<void>;
  resumeSession: (sessionId: string) => Promise<void>;
  endSession: (sessionId: string, summary?: SessionSummary) => Promise<TimerSession>;
  
  // Real-time subscriptions
  subscribeToRealtime: (userId: string) => void;
  unsubscribeFromRealtime: () => void;
  
  // Utility methods
  getSessionsByDateRange: (startDate: Date, endDate: Date) => TimerSession[];
  getSessionsBySubject: (subject: string) => TimerSession[];
  getTotalStudyTime: () => number;
  getSessionCount: () => number;
  clearError: () => void;
  clearSessions: () => void;
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export const useSessionStore = create<SessionState>()(
  persist(
    (set, get) => ({
      // Initial state
      sessions: [],
      currentSession: null,
      isLoading: false,
      error: null,
      lastFetch: null,

      // Fetch sessions with caching
      fetchSessions: async (userId: string, forceRefresh = false) => {
        const state = get();
        const now = Date.now();
        
        // Check cache validity
        if (!forceRefresh && state.lastFetch && (now - state.lastFetch) < CACHE_DURATION) {
          return;
        }

        set({ isLoading: true, error: null });

        try {
          const response = await enhancedSupabaseService.getStudySessions(userId);
          
          if (response.success && response.data) {
            set({
              sessions: response.data,
              isLoading: false,
              lastFetch: now,
              error: null,
            });
          } else {
            throw new Error(response.error || 'Failed to fetch sessions');
          }
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Failed to fetch sessions',
          });
          throw error;
        }
      },

      // Create new session
      createSession: async (userId: string, sessionData: Omit<TimerSession, 'id'>) => {
        set({ isLoading: true, error: null });

        try {
          const response = await enhancedSupabaseService.createStudySession(userId, sessionData);
          
          if (response.success && response.data) {
            const newSession = response.data;
            
            set(state => ({
              sessions: [newSession, ...state.sessions],
              isLoading: false,
              error: null,
            }));
            
            return newSession;
          } else {
            throw new Error(response.error || 'Failed to create session');
          }
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Failed to create session',
          });
          throw error;
        }
      },

      // Update existing session
      updateSession: async (sessionId: string, updates: Partial<TimerSession>) => {
        set({ isLoading: true, error: null });

        try {
          const response = await enhancedSupabaseService.updateStudySession(sessionId, updates);
          
          if (response.success && response.data) {
            const updatedSession = response.data;
            
            set(state => ({
              sessions: state.sessions.map(session =>
                session.id === sessionId ? updatedSession : session
              ),
              currentSession: state.currentSession?.id === sessionId ? updatedSession : state.currentSession,
              isLoading: false,
              error: null,
            }));
            
            return updatedSession;
          } else {
            throw new Error(response.error || 'Failed to update session');
          }
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Failed to update session',
          });
          throw error;
        }
      },

      // Complete session with summary
      completeSession: async (sessionId: string, summary: SessionSummary) => {
        const updates: Partial<TimerSession> = {
          completed: true,
          endTime: new Date(),
          notes: summary.notes,
          feedback: summary.feedback,
          productivityRating: summary.productivityRating,
          taskName: summary.taskName,
          taskType: summary.taskType,
        };

        return get().updateSession(sessionId, updates);
      },

      // Start new session
      startSession: async (userId: string, sessionData: Partial<TimerSession>) => {
        const now = new Date();
        const today = now.toISOString().split('T')[0];

        const newSessionData: Omit<TimerSession, 'id'> = {
          startTime: now,
          endTime: null,
          duration: 0,
          mode: 'stopwatch',
          completed: false,
          date: today,
          ...sessionData,
        };

        const session = await get().createSession(userId, newSessionData);
        set({ currentSession: session });
        return session;
      },

      // Pause current session
      pauseSession: async (sessionId: string) => {
        const state = get();
        if (state.currentSession?.id === sessionId) {
          // Update local state immediately for responsiveness
          set(state => ({
            currentSession: state.currentSession ? {
              ...state.currentSession,
              // Add pause logic here if needed
            } : null,
          }));
        }
      },

      // Resume current session
      resumeSession: async (sessionId: string) => {
        const state = get();
        if (state.currentSession?.id === sessionId) {
          // Update local state immediately for responsiveness
          set(state => ({
            currentSession: state.currentSession ? {
              ...state.currentSession,
              // Add resume logic here if needed
            } : null,
          }));
        }
      },

      // End current session
      endSession: async (sessionId: string, summary?: SessionSummary) => {
        const now = new Date();
        const state = get();
        
        let updates: Partial<TimerSession> = {
          endTime: now,
          completed: true,
        };

        // Calculate duration if current session
        if (state.currentSession?.id === sessionId && state.currentSession.startTime) {
          const duration = Math.floor((now.getTime() - state.currentSession.startTime.getTime()) / 1000);
          updates.duration = duration;
        }

        // Add summary data if provided
        if (summary) {
          updates = {
            ...updates,
            notes: summary.notes,
            feedback: summary.feedback,
            productivityRating: summary.productivityRating,
            taskName: summary.taskName,
            taskType: summary.taskType,
          };
        }

        const updatedSession = await get().updateSession(sessionId, updates);
        
        // Clear current session if it was the one being ended
        if (state.currentSession?.id === sessionId) {
          set({ currentSession: null });
        }
        
        return updatedSession;
      },

      // Real-time subscriptions
      subscribeToRealtime: (userId: string) => {
        enhancedSupabaseService.subscribeToStudySessions(userId, (sessions) => {
          set({ sessions, lastFetch: Date.now() });
        });
      },

      unsubscribeFromRealtime: () => {
        enhancedSupabaseService.unsubscribeFromAll();
      },

      // Utility methods
      getSessionsByDateRange: (startDate: Date, endDate: Date) => {
        const state = get();
        return state.sessions.filter(session => {
          const sessionDate = new Date(session.date);
          return sessionDate >= startDate && sessionDate <= endDate;
        });
      },

      getSessionsBySubject: (subject: string) => {
        const state = get();
        return state.sessions.filter(session => session.subject === subject);
      },

      getTotalStudyTime: () => {
        const state = get();
        return state.sessions.reduce((total, session) => total + session.duration, 0);
      },

      getSessionCount: () => {
        const state = get();
        return state.sessions.length;
      },

      clearError: () => {
        set({ error: null });
      },

      clearSessions: () => {
        set({
          sessions: [],
          currentSession: null,
          lastFetch: null,
          error: null,
        });
      },
    }),
    {
      name: 'session-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        sessions: state.sessions,
        currentSession: state.currentSession,
        lastFetch: state.lastFetch,
      }),
    }
  )
);
